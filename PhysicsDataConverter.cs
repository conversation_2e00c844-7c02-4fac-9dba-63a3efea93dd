//PhysicsDataConverter.cs
using System;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json;
using UnityEngine;
using UnityEditor;

namespace PhysicsDataConverter
{

    [Serializable]
    public class UnityPhysicsExportData
    {
        public List<UnityPhysicsBodyData> PhysicsBodies { get; set; } = new List<UnityPhysicsBodyData>();
        public List<UnityPhysicsConstraintData> PhysicsConstraints { get; set; } = new List<UnityPhysicsConstraintData>();
        public List<UnityPhysicsMaterialData> PhysicsMaterials { get; set; } = new List<UnityPhysicsMaterialData>();
    }

    [Serializable]
    public class UnityPhysicsBodyData
    {
        public string BoneName { get; set; }
        public string GameObjectName { get; set; }
        public bool IsKinematic { get; set; }
        public float Mass { get; set; }
        public float Drag { get; set; }
        public float AngularDrag { get; set; }
        public bool UseGravity { get; set; }
        public UnityEngine.Vector3 LocalPosition { get; set; }
        public UnityEngine.Vector3 LocalRotationEuler { get; set; }
        public UnityEngine.Vector3 LocalScale { get; set; }
        public List<UnityColliderData> Colliders { get; set; } = new List<UnityColliderData>();
    }

    [Serializable]
    public class UnityColliderData
    {
        public string Type { get; set; }
        public UnityEngine.Vector3 CenterRelativeToBone { get; set; }
        public UnityEngine.Quaternion RotationRelativeToBone { get; set; }
        public UnityEngine.Vector3 ScaleRelativeToBone { get; set; }
        public UnityEngine.Vector3 OriginalSize { get; set; }
        public float OriginalRadius { get; set; }
        public float OriginalHeight { get; set; }
        public int OriginalDirection { get; set; }
        public string MaterialName { get; set; }
        public bool IsTrigger { get; set; }
        public string BoneName { get; set; } // Added to store the bone name this collider is associated with
    }

    [Serializable]
    public class UnityPhysicsConstraintData
    {
        public string Type { get; set; }
        public string GameObjectName { get; set; }
        public string ConnectedBodyName { get; set; }
        public UnityEngine.Vector3 Anchor { get; set; }
        public UnityEngine.Vector3 ConnectedAnchor { get; set; }
        public UnityEngine.Vector3 Axis { get; set; }
        public UnityEngine.Vector3 SecondaryAxis { get; set; }
        public bool EnableCollision { get; set; }
        public float BreakForce { get; set; }
        public float BreakTorque { get; set; }
        public bool EnablePreprocessing { get; set; }
        public float MassScale { get; set; }
        public float ConnectedMassScale { get; set; }


        public bool UseLimits { get; set; }
        public float MinLimit { get; set; }
        public float MaxLimit { get; set; }
        public bool UseMotor { get; set; }
        public float MotorForce { get; set; }
        public float MotorVelocity { get; set; }


        public float Swing1Limit { get; set; }
        public float Swing2Limit { get; set; }
        public float LowTwistLimit { get; set; }
        public float HighTwistLimit { get; set; }


        public ConfigurableJointMotion XMotion { get; set; }
        public ConfigurableJointMotion YMotion { get; set; }
        public ConfigurableJointMotion ZMotion { get; set; }
        public ConfigurableJointMotion AngularXMotion { get; set; }
        public ConfigurableJointMotion AngularYMotion { get; set; }
        public ConfigurableJointMotion AngularZMotion { get; set; }
        public float LinearLimit { get; set; }
        public float AngularYLimit { get; set; }
        public float AngularZLimit { get; set; }
        public float LowAngularXLimit { get; set; }
        public float HighAngularXLimit { get; set; }
    }

    [Serializable]
    public class UnityPhysicsMaterialData
    {
        public string name { get; set; }
        public float dynamicFriction { get; set; }
        public float staticFriction { get; set; }
        public float bounciness { get; set; }
        public PhysicMaterialCombine frictionCombine { get; set; }
        public PhysicMaterialCombine bounceCombine { get; set; }
    }


    [Serializable]
    public class UE5PhysicsExportData
    {
        public string PhysicsAssetName { get; set; }
        public string SkeletalMeshName { get; set; }
        public List<UE5PhysicsBody> PhysicsBodies { get; set; } = new List<UE5PhysicsBody>();
        public List<UE5PhysicsConstraint> PhysicsConstraints { get; set; } = new List<UE5PhysicsConstraint>();
        public List<UE5CollisionDisableEntry> CollisionDisableTable { get; set; } = new List<UE5CollisionDisableEntry>();
    }

    [Serializable]
    public class UE5PhysicsBody
    {
        public string BoneName { get; set; }
        public int BodyIndex { get; set; }
        public string PhysicsType { get; set; }
        public bool bConsiderForBounds { get; set; }
        public bool bDoubleSidedGeometry { get; set; }
        public bool bGenerateNonMirroredCollision { get; set; }
        public bool bSharedCookedData { get; set; }
        public List<UE5CollisionShape> CollisionShapes { get; set; } = new List<UE5CollisionShape>();
        public UE5BodyInstance BodyInstance { get; set; }
    }

    [Serializable]
    public class UE5CollisionShape
    {
        public string Type { get; set; }
        public UE5Vector Center { get; set; } = new UE5Vector { X = 0, Y = 0, Z = 0 };
        public UE5Rotator Rotation { get; set; } = new UE5Rotator { Pitch = 0, Yaw = 0, Roll = 0 };
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }
        public float Radius { get; set; }
        public float Length { get; set; }
        public List<UE5Vector> VertexData { get; set; } = new List<UE5Vector>();
        public string BoneName { get; set; } // Added to store specific bone association for colliders
    }

    [Serializable]
    public class UE5BodyInstance
    {
        public float MassInKg { get; set; }
        public bool bOverrideMass { get; set; }
        public float LinearDamping { get; set; }
        public float AngularDamping { get; set; }
        public bool bSimulatePhysics { get; set; }
        public bool bGenerateWakeEvents { get; set; }
        public bool bStartAwake { get; set; }
        public string CollisionProfileName { get; set; }
        public int CollisionEnabled { get; set; }
        public UE5PhysicalMaterial PhysicalMaterial { get; set; }
    }

    [Serializable]
    public class UE5PhysicalMaterial
    {
        public string MaterialName { get; set; }
        public float Density { get; set; }
        public float Friction { get; set; }
        public float Restitution { get; set; }
        public float SleepLinearVelocityThreshold { get; set; }
        public float SleepAngularVelocityThreshold { get; set; }
    }

    [Serializable]
    public class UE5PhysicsConstraint
    {
        public int ConstraintIndex { get; set; }
        public string ConstraintName { get; set; }
        public string Bone1 { get; set; }
        public string Bone2 { get; set; }
        public UE5Transform Frame1Transform { get; set; }
        public UE5Transform Frame2Transform { get; set; }
        public UE5LinearLimits LinearLimits { get; set; }
        public UE5AngularLimits AngularLimits { get; set; }
        public UE5LinearDrive LinearXDrive { get; set; }
        public UE5LinearDrive LinearYDrive { get; set; }
        public UE5LinearDrive LinearZDrive { get; set; }
        public UE5Vector LinearPositionTarget { get; set; }
        public UE5Vector LinearVelocityTarget { get; set; }
        public string AngularDriveMode { get; set; }
        public UE5AngularDrive SlerpDrive { get; set; }
        public UE5AngularDrive SwingDrive { get; set; }
        public UE5AngularDrive TwistDrive { get; set; }
        public UE5Quaternion AngularOrientationTarget { get; set; }
        public UE5Vector AngularVelocityTarget { get; set; }
        public bool bDisableCollision { get; set; }
        public bool bEnableProjection { get; set; }
        public float ProjectionLinearTolerance { get; set; }
        public float ProjectionAngularTolerance { get; set; }
    }

    [Serializable]
    public class UE5LinearLimits
    {
        public string XMotion { get; set; }
        public string YMotion { get; set; }
        public string ZMotion { get; set; }
        public float LinearLimitX { get; set; }
        public float LinearLimitY { get; set; }
        public float LinearLimitZ { get; set; }
    }

    [Serializable]
    public class UE5AngularLimits
    {
        public string Swing1Motion { get; set; }
        public string Swing2Motion { get; set; }
        public string TwistMotion { get; set; }
        public float Swing1LimitAngle { get; set; }
        public float Swing2LimitAngle { get; set; }
        public float TwistLimitAngle { get; set; }
    }

    [Serializable]
    public class UE5LinearDrive
    {
        public bool bEnablePositionDrive { get; set; }
        public bool bEnableVelocityDrive { get; set; }
    }

    [Serializable]
    public class UE5AngularDrive
    {
        public bool bEnablePositionDrive { get; set; }
        public bool bEnableVelocityDrive { get; set; }
    }

    [Serializable]
    public class UE5CollisionDisableEntry
    {
        public int BodyIndex1 { get; set; }
        public int BodyIndex2 { get; set; }
    }


    [Serializable]
    public class UE5Vector
    {
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }


        public static UE5Vector operator *(UE5Vector vector, float scalar)
        {
            return new UE5Vector
            {
                X = vector.X * scalar,
                Y = vector.Y * scalar,
                Z = vector.Z * scalar
            };
        }
    }

    [Serializable]
    public class UE5Rotator
    {
        public float Pitch { get; set; }
        public float Yaw { get; set; }
        public float Roll { get; set; }
    }

    [Serializable]
    public class UE5Transform
    {
        public UE5Vector Location { get; set; }
        public UE5Rotator Rotation { get; set; }
        public UE5Vector Scale { get; set; }
    }

    [Serializable]
    public class UE5Quaternion
    {
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }
        public float W { get; set; }
    }

    public class PhysicsDataConverter
    {
        private JsonSerializerSettings jsonSettings;

        public PhysicsDataConverter()
        {
            jsonSettings = new JsonSerializerSettings
            {
                Formatting = Formatting.Indented,
                NullValueHandling = NullValueHandling.Ignore,
                Converters = new List<JsonConverter> { new Newtonsoft.Json.Converters.StringEnumConverter() }
            };
        }

        public UnityPhysicsExportData ConvertUE5ToUnity(string ue5JsonPath)
        {
            try
            {
                string jsonContent = File.ReadAllText(ue5JsonPath);
                var ue5Data = JsonConvert.DeserializeObject<UE5PhysicsExportData>(jsonContent, jsonSettings);
                return ConvertUE5ToUnity(ue5Data);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting UE5 to Unity: {ex.Message}");
                throw;
            }
        }

        public UE5PhysicsExportData ConvertUnityToUE5(string unityJsonPath)
        {
            try
            {
                string jsonContent = File.ReadAllText(unityJsonPath);
                var unityData = JsonConvert.DeserializeObject<UnityPhysicsExportData>(jsonContent, jsonSettings);
                return ConvertUnityToUE5(unityData);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting Unity to UE5: {ex.Message}");
                throw;
            }
        }

        private UnityPhysicsExportData ConvertUE5ToUnity(UE5PhysicsExportData ue5Data)
        {
            var unityData = new UnityPhysicsExportData();
            var materialMap = new Dictionary<string, UnityPhysicsMaterialData>();
            const float CmToMeters = 0.01f;


            foreach (var ue5Body in ue5Data.PhysicsBodies)
            {
                if (ue5Body.BodyInstance?.PhysicalMaterial != null)
                {
                    var ue5Material = ue5Body.BodyInstance.PhysicalMaterial;
                    if (!materialMap.ContainsKey(ue5Material.MaterialName))
                    {
                        var unityMaterial = new UnityPhysicsMaterialData
                        {
                            name = ue5Material.MaterialName,
                            dynamicFriction = ue5Material.Friction,
                            staticFriction = ue5Material.Friction,
                            bounciness = ue5Material.Restitution,
                            frictionCombine = PhysicMaterialCombine.Average,
                            bounceCombine = PhysicMaterialCombine.Average
                        };
                        materialMap[ue5Material.MaterialName] = unityMaterial;
                        unityData.PhysicsMaterials.Add(unityMaterial);
                    }
                }
            }


            foreach (var ue5Body in ue5Data.PhysicsBodies)
            {
                var unityBody = new UnityPhysicsBodyData
                {
                    BoneName = ue5Body.BoneName,
                    GameObjectName = ue5Body.BoneName,
                    IsKinematic = ue5Body.PhysicsType == "PhysType_Kinematic",
                    Mass = ue5Body.BodyInstance?.MassInKg ?? 1.0f,
                    Drag = ue5Body.BodyInstance?.LinearDamping ?? 0.0f,
                    AngularDrag = ue5Body.BodyInstance?.AngularDamping ?? 0.05f,
                    UseGravity = true,
                    LocalPosition = UnityEngine.Vector3.zero,
                    LocalRotationEuler = UnityEngine.Vector3.zero,
                    LocalScale = UnityEngine.Vector3.one
                };

                foreach (var ue5Shape in ue5Body.CollisionShapes)
                {
                    var unityCollider = new UnityColliderData
                    {
                        Type = ue5Shape.Type,
                        CenterRelativeToBone = ConvertUE5ToUnityVector(ue5Shape.Center * CmToMeters),
                        RotationRelativeToBone = ConvertUE5RotatorToQuaternion(ue5Shape.Rotation),
                        MaterialName = ue5Body.BodyInstance?.PhysicalMaterial?.MaterialName ?? "Default",
                        IsTrigger = false,
                        // Preserve the bone association if it exists, otherwise use the body's bone name
                        BoneName = !string.IsNullOrEmpty(ue5Shape.BoneName) ? ue5Shape.BoneName : ue5Body.BoneName
                    };

                    switch (ue5Shape.Type.ToLower())
                    {
                        case "box":
                            float unitySizeX = ue5Shape.X * CmToMeters;
                            float unitySizeY = ue5Shape.Y * CmToMeters;
                            float unitySizeZ = ue5Shape.Z * CmToMeters;
                            unityCollider.OriginalSize = new UnityEngine.Vector3(unitySizeX, unitySizeY, unitySizeZ);
                            break;
                        case "sphere":
                            unityCollider.OriginalRadius = ue5Shape.Radius * CmToMeters;
                            break;
                        case "capsule":
                            unityCollider.OriginalRadius = ue5Shape.Radius * CmToMeters;
                            float cylinderHeight = ue5Shape.Length * 2.0f * CmToMeters;
                            unityCollider.OriginalHeight = cylinderHeight + (unityCollider.OriginalRadius * 2.0f);

                            UnityEngine.Quaternion relativeRot = unityCollider.RotationRelativeToBone;
                            UnityEngine.Vector3 capsuleUp = relativeRot * UnityEngine.Vector3.up;

                            float dotX = Mathf.Abs(Vector3.Dot(capsuleUp, Vector3.right));
                            float dotY = Mathf.Abs(Vector3.Dot(capsuleUp, Vector3.up));
                            float dotZ = Mathf.Abs(Vector3.Dot(capsuleUp, Vector3.forward));

                            if (dotY > dotX && dotY > dotZ)
                            {
                                unityCollider.OriginalDirection = 1;
                            }
                            else if (dotZ > dotX && dotZ > dotY)
                            {
                                unityCollider.OriginalDirection = 2;
                            }
                            else
                            {
                                unityCollider.OriginalDirection = 0;
                            }
                            break;
                        default:
                             Debug.LogWarning($"Unsupported UE5 collider type '{ue5Shape.Type}' encountered for body '{ue5Body.BoneName}'. Skipping shape.");
                             continue; // Skip adding this shape
                    }

                    unityBody.Colliders.Add(unityCollider);
                }

                unityData.PhysicsBodies.Add(unityBody);
            }


            foreach (var ue5Constraint in ue5Data.PhysicsConstraints)
            {
                var unityConstraint = new UnityPhysicsConstraintData
                {
                    GameObjectName = ue5Constraint.Bone1,
                    ConnectedBodyName = ue5Constraint.Bone2,
                    Anchor = ConvertUE5ToUnityVector(ue5Constraint.Frame1Transform.Location),
                    ConnectedAnchor = ConvertUE5ToUnityVector(ue5Constraint.Frame2Transform.Location),
                    EnableCollision = !ue5Constraint.bDisableCollision,
                    EnablePreprocessing = ue5Constraint.bEnableProjection,
                    MassScale = 1.0f,
                    ConnectedMassScale = 1.0f
                };


                if (IsFixedJoint(ue5Constraint))
                {
                    unityConstraint.Type = "FixedJoint";
                }
                else if (IsHingeJoint(ue5Constraint))
                {
                    unityConstraint.Type = "HingeJoint";
                    SetHingeJointProperties(ue5Constraint, unityConstraint);
                }
                else if (IsCharacterJoint(ue5Constraint))
                {
                    unityConstraint.Type = "CharacterJoint";
                    SetCharacterJointProperties(ue5Constraint, unityConstraint);
                }
                else
                {
                    unityConstraint.Type = "ConfigurableJoint";
                    SetConfigurableJointProperties(ue5Constraint, unityConstraint);
                }

                unityData.PhysicsConstraints.Add(unityConstraint);
            }

            return unityData;
        }

        private bool IsFixedJoint(UE5PhysicsConstraint constraint)
        {
            return constraint.LinearLimits.XMotion == "LCM_Locked" &&
                   constraint.LinearLimits.YMotion == "LCM_Locked" &&
                   constraint.LinearLimits.ZMotion == "LCM_Locked" &&
                   constraint.AngularLimits.Swing1Motion == "ACM_Locked" &&
                   constraint.AngularLimits.Swing2Motion == "ACM_Locked" &&
                   constraint.AngularLimits.TwistMotion == "ACM_Locked";
        }

        private bool IsHingeJoint(UE5PhysicsConstraint constraint)
        {
            return constraint.LinearLimits.XMotion == "LCM_Locked" &&
                   constraint.LinearLimits.YMotion == "LCM_Locked" &&
                   constraint.LinearLimits.ZMotion == "LCM_Locked" &&
                   constraint.AngularLimits.Swing1Motion == "ACM_Locked" &&
                   constraint.AngularLimits.Swing2Motion == "ACM_Locked" &&
                   (constraint.AngularLimits.TwistMotion == "ACM_Limited" || constraint.AngularLimits.TwistMotion == "ACM_Free");
        }

        private bool IsCharacterJoint(UE5PhysicsConstraint constraint)
        {
            return constraint.LinearLimits.XMotion == "LCM_Locked" &&
                   constraint.LinearLimits.YMotion == "LCM_Locked" &&
                   constraint.LinearLimits.ZMotion == "LCM_Locked" &&
                   constraint.AngularLimits.Swing1Motion == "ACM_Limited" &&
                   constraint.AngularLimits.Swing2Motion == "ACM_Limited" &&
                   constraint.AngularLimits.TwistMotion == "ACM_Limited";
        }

        private void SetHingeJointProperties(UE5PhysicsConstraint ue5Constraint, UnityPhysicsConstraintData unityConstraint)
        {
            ue5Constraint.AngularLimits = new UE5AngularLimits
            {
                Swing1Motion = "ACM_Locked",
                Swing2Motion = "ACM_Locked",
                TwistMotion = unityConstraint.UseLimits ? "ACM_Limited" : "ACM_Free"
            };

            if (unityConstraint.UseLimits)
            {
                ue5Constraint.AngularLimits.TwistLimitAngle = unityConstraint.MaxLimit;
            }

            if (unityConstraint.UseMotor)
            {
                ue5Constraint.TwistDrive = new UE5AngularDrive
                {
                    bEnablePositionDrive = false,
                    bEnableVelocityDrive = true
                };
                ue5Constraint.AngularVelocityTarget = new UE5Vector
                {
                    X = unityConstraint.MotorVelocity,
                    Y = 0,
                    Z = 0
                };
            }
        }

        private void SetCharacterJointProperties(UE5PhysicsConstraint ue5Constraint, UnityPhysicsConstraintData unityConstraint)
        {
            ue5Constraint.AngularLimits = new UE5AngularLimits
            {
                Swing1Motion = "ACM_Limited",
                Swing2Motion = "ACM_Limited",
                TwistMotion = "ACM_Limited",
                Swing1LimitAngle = (unityConstraint.HighTwistLimit - unityConstraint.LowTwistLimit) / 2.0f,
                Swing2LimitAngle = unityConstraint.Swing1Limit,
                TwistLimitAngle = unityConstraint.Swing2Limit
            };
        }

        private void SetConfigurableJointProperties(UE5PhysicsConstraint ue5Constraint, UnityPhysicsConstraintData unityConstraint)
        {
            ue5Constraint.LinearLimits = new UE5LinearLimits
            {
                XMotion = ConvertUnityEnumMotionToUE5String(unityConstraint.XMotion, false),
                YMotion = ConvertUnityEnumMotionToUE5String(unityConstraint.YMotion, false),
                ZMotion = ConvertUnityEnumMotionToUE5String(unityConstraint.ZMotion, false),
                LinearLimitX = unityConstraint.LinearLimit,
                LinearLimitY = unityConstraint.LinearLimit,
                LinearLimitZ = unityConstraint.LinearLimit
            };

            ue5Constraint.AngularLimits = new UE5AngularLimits
            {
                Swing1Motion = ConvertUnityEnumMotionToUE5String(unityConstraint.AngularXMotion, true),
                Swing2Motion = ConvertUnityEnumMotionToUE5String(unityConstraint.AngularYMotion, true),
                TwistMotion = ConvertUnityEnumMotionToUE5String(unityConstraint.AngularZMotion, true),
                Swing1LimitAngle = unityConstraint.AngularXMotion == ConfigurableJointMotion.Limited ? (unityConstraint.HighAngularXLimit - unityConstraint.LowAngularXLimit) / 2.0f : 0,
                Swing2LimitAngle = unityConstraint.AngularYMotion == ConfigurableJointMotion.Limited ? unityConstraint.AngularYLimit : 0,
                TwistLimitAngle = unityConstraint.AngularZMotion == ConfigurableJointMotion.Limited ? unityConstraint.AngularZLimit : 0
            };
        }

        private string ConvertUnityEnumMotionToUE5String(ConfigurableJointMotion unityMotion, bool isAngular)
        {
            switch (unityMotion)
            {
                case ConfigurableJointMotion.Locked:
                    return isAngular ? "ACM_Locked" : "LCM_Locked";
                case ConfigurableJointMotion.Limited:
                    return isAngular ? "ACM_Limited" : "LCM_Limited";
                case ConfigurableJointMotion.Free:
                    return isAngular ? "ACM_Free" : "LCM_Free";
                default:
                    Debug.LogWarning($"Unknown Unity motion type: {unityMotion}. Defaulting to Locked.");
                    return isAngular ? "ACM_Locked" : "LCM_Locked";
            }
        }

        private UE5PhysicsExportData ConvertUnityToUE5(UnityPhysicsExportData unityData)
        {
            var ue5Data = new UE5PhysicsExportData();
            var bodyIndexMap = new Dictionary<string, int>();
            var materialCache = new Dictionary<string, UE5PhysicalMaterial>();
            const float MetersToCm = 100.0f;


            foreach (var unityMaterial in unityData.PhysicsMaterials)
            {
                if (!materialCache.ContainsKey(unityMaterial.name))
                {
                    materialCache[unityMaterial.name] = new UE5PhysicalMaterial
                    {
                        MaterialName = unityMaterial.name,
                        Friction = unityMaterial.dynamicFriction,
                        Restitution = unityMaterial.bounciness,
                        Density = 1.0f,
                        SleepAngularVelocityThreshold = 0.005f,
                        SleepLinearVelocityThreshold = 0.005f
                    };
                }
            }


            for (int i = 0; i < unityData.PhysicsBodies.Count; i++)
            {
                var unityBody = unityData.PhysicsBodies[i];
                var ue5Body = new UE5PhysicsBody
                {
                    BoneName = unityBody.BoneName,
                    BodyIndex = i,
                    PhysicsType = unityBody.IsKinematic ? "PhysType_Kinematic" : "PhysType_Simulated",
                    bConsiderForBounds = true,
                    bDoubleSidedGeometry = false,
                    bGenerateNonMirroredCollision = false,
                    bSharedCookedData = false,
                    BodyInstance = new UE5BodyInstance
                    {
                        MassInKg = unityBody.Mass,
                        bOverrideMass = true,
                        LinearDamping = unityBody.Drag,
                        AngularDamping = unityBody.AngularDrag,
                        bSimulatePhysics = !unityBody.IsKinematic,
                        bGenerateWakeEvents = true,
                        bStartAwake = true,
                        CollisionProfileName = "PhysicsActor",
                        CollisionEnabled = 1,
                        PhysicalMaterial = FindBestMatchingUE5Material(unityBody, materialCache)
                    }
                };


                foreach (var unityCollider in unityBody.Colliders)
                {
                    // Check if the collider has a specific bone name that differs from the body's bone name
                    string colliderBoneName = unityCollider.BoneName;
                    bool useCustomBone = !string.IsNullOrEmpty(colliderBoneName) && colliderBoneName != unityBody.BoneName;

                    var ue5Shape = new UE5CollisionShape
                    {
                        Type = unityCollider.Type,
                        Center = ConvertUnityToUE5Vector(unityCollider.CenterRelativeToBone * MetersToCm),
                        Rotation = ConvertUnityToUE5Rotator(unityCollider.RotationRelativeToBone)
                    };

                    // Store the bone name this collider is associated with
                    if (useCustomBone)
                    {
                        ue5Shape.BoneName = colliderBoneName;
                    }

                    UnityEngine.Vector3 relativeScaleUE = new UnityEngine.Vector3(
                        unityCollider.ScaleRelativeToBone.x,
                        unityCollider.ScaleRelativeToBone.z,
                        unityCollider.ScaleRelativeToBone.y
                    );


                    switch (unityCollider.Type.ToLower())
                    {
                        case "box":

                            UnityEngine.Vector3 originalSize = unityCollider.OriginalSize;
                            UnityEngine.Vector3 relativeScale = unityCollider.ScaleRelativeToBone;
                            float scaledUnityX = originalSize.x * relativeScale.x;
                            float scaledUnityY = originalSize.y * relativeScale.y;
                            float scaledUnityZ = originalSize.z * relativeScale.z;


                            ue5Shape.X = (scaledUnityX) * MetersToCm;
                            ue5Shape.Y = (scaledUnityZ) * MetersToCm;
                            ue5Shape.Z = (scaledUnityY) * MetersToCm;
                            break;
                        case "sphere":

                            float radiusScale = Mathf.Max(relativeScaleUE.x, relativeScaleUE.y, relativeScaleUE.z);
                            ue5Shape.Radius = unityCollider.OriginalRadius * radiusScale * MetersToCm;

                            if (Mathf.Abs(relativeScaleUE.x - relativeScaleUE.y) > 0.01f || Mathf.Abs(relativeScaleUE.x - relativeScaleUE.z) > 0.01f)
                            {
                                Debug.LogWarning($"Non-uniform scale detected for Sphere collider on '{unityBody.BoneName}'. UE5 conversion will approximate using max scale component.");
                            }
                            break;
                        case "capsule":


                            float radiusScaleCap = Mathf.Max(relativeScaleUE.x, relativeScaleUE.y);
                            float heightScaleCap = relativeScaleUE.z;

                            float scaledRadius = unityCollider.OriginalRadius * radiusScaleCap;
                            float scaledHeight = unityCollider.OriginalHeight * heightScaleCap;

                            ue5Shape.Radius = scaledRadius * MetersToCm;

                            float length = scaledHeight - scaledRadius * 2f;
                            ue5Shape.Length = length > 0 ? length * MetersToCm : 0f;
                           
                            Quaternion addRot = Quaternion.identity;
                            if (unityCollider.OriginalDirection == 0) // Unity X-axis
                            {                             
                                addRot = Quaternion.Euler(0, 90, 0);
                            }
                            else if (unityCollider.OriginalDirection == 1) // Unity Y-axis
                            {
                                // Y 轴已经是目标轴，无需额外旋转
                                addRot = Quaternion.identity;
                            }
                            else if (unityCollider.OriginalDirection == 2) // Unity Z-axis
                            {
                                // 将 Z 轴旋转到 Y 轴 (绕 X 轴旋转 -90 度)
                                addRot = Quaternion.Euler(-90, 0, 0);
                            }

                            Quaternion finalRot = unityCollider.RotationRelativeToBone * addRot;
                            ue5Shape.Rotation = ConvertUnityToUE5Rotator(finalRot);

                            if (Mathf.Abs(relativeScaleUE.x - relativeScaleUE.y) > 0.01f)
                            {
                                Debug.LogWarning($"Non-uniform XY scale detected for Capsule collider on '{unityBody.BoneName}'. UE5 conversion will approximate radius scale using max(X, Y).");
                            }
                            break;
                        default:
                            Debug.LogWarning($"Unsupported collider type '{unityCollider.Type}' encountered for body '{unityBody.BoneName}'. Skipping shape.");
                            continue;
                    }


                    if (ue5Body.BodyInstance.PhysicalMaterial == null && !string.IsNullOrEmpty(unityCollider.MaterialName) && materialCache.TryGetValue(unityCollider.MaterialName, out var mat))
                    {
                        ue5Body.BodyInstance.PhysicalMaterial = mat;
                    }

                    else if (ue5Body.BodyInstance.PhysicalMaterial == null && !string.IsNullOrEmpty(unityCollider.MaterialName) && unityCollider.MaterialName != "Default")
                    {
                        Debug.LogWarning($"Could not find or assign UE5 physical material '{unityCollider.MaterialName}' for body '{unityBody.BoneName}' based on collider.");
                    }

                    ue5Body.CollisionShapes.Add(ue5Shape);
                }


                if (ue5Body.BodyInstance.PhysicalMaterial == null)
                {
                    Debug.LogWarning($"Body '{unityBody.BoneName}' has no physical material assigned after processing colliders. Consider adding a default material.");
                    ue5Body.BodyInstance.PhysicalMaterial = new UE5PhysicalMaterial { MaterialName = "DefaultPhysicalMaterial", Friction = 0.7f, Restitution = 0.1f, Density = 1.0f };
                }

                ue5Data.PhysicsBodies.Add(ue5Body);
                bodyIndexMap[unityBody.BoneName] = i;
            }


            // 创建一个从GameObject名称到物理体数据的映射
            var bodyDataMap = new Dictionary<string, UnityPhysicsBodyData>();
            foreach (var body in unityData.PhysicsBodies)
            {
                bodyDataMap[body.GameObjectName] = body;
            }

            foreach (var unityConstraint in unityData.PhysicsConstraints)
            {
                // 如果 ConnectedBodyName 为空，说明这个约束在 Unity 中没有连接到任何其他刚体
                // 这在 UE5 的骨骼约束中通常是无效的，所以我们跳过它
                if (string.IsNullOrEmpty(unityConstraint.ConnectedBodyName))
                {
                    Debug.LogWarning($"Skipping constraint '{unityConstraint.GameObjectName}' of type '{unityConstraint.Type}' because its ConnectedBodyName is empty (likely not connected in Unity).");
                    continue; // 跳到下一个约束
                }

                float d_val = (unityConstraint.HighTwistLimit + unityConstraint.LowTwistLimit) / 2.0f;
                UE5Rotator frame1Rotation;

                // 根据 unityConstraint.Axis 设置 Frame1Transform.Rotation
                // (1,0,0) 对应 UnityEngine.Vector3.right
                // (0,1,0) 对应 UnityEngine.Vector3.up
                // (0,0,1) 对应 UnityEngine.Vector3.forward
                if (unityConstraint.Axis == UnityEngine.Vector3.right)
                {
                    frame1Rotation = new UE5Rotator { Roll = -90f, Pitch = d_val, Yaw = 90f };
                }
                else if (unityConstraint.Axis == UnityEngine.Vector3.up)
                {
                    frame1Rotation = new UE5Rotator { Roll = 180f, Pitch = 0f, Yaw = 90f - d_val };
                }
                else if (unityConstraint.Axis == UnityEngine.Vector3.forward)
                {
                    frame1Rotation = new UE5Rotator { Roll = -90f, Pitch = d_val, Yaw = 0f };
                }
                else // 如果 Axis 不是特定值，则使用原始逻辑作为默认值
                {
                    frame1Rotation = new UE5Rotator { Roll = -90f, Pitch = d_val, Yaw = 90f };
                }

                var ue5Constraint = new UE5PhysicsConstraint
                {
                    ConstraintName = unityConstraint.GameObjectName,
                    Bone1 = unityConstraint.GameObjectName,
                    Bone2 = unityConstraint.ConnectedBodyName,
                    Frame1Transform = new UE5Transform
                    {
                        Location = ConvertUnityToUE5Vector(unityConstraint.Anchor * MetersToCm),
                        Rotation = frame1Rotation,
                        Scale = new UE5Vector { X = 1, Y = 1, Z = 1 }
                    },
                    Frame2Transform = CreateFrame2TransformFromBodyData(unityConstraint, bodyDataMap, MetersToCm),
                    bDisableCollision = !unityConstraint.EnableCollision,
                    bEnableProjection = unityConstraint.EnablePreprocessing,
                    ProjectionLinearTolerance = 0.1f,
                    ProjectionAngularTolerance = 0.1f
                };


                switch (unityConstraint.Type.ToLower())
                {
                    case "fixedjoint":
                        SetFixedJointProperties(ue5Constraint, unityConstraint);
                        break;
                    case "hingejoint":
                        SetHingeJointProperties(ue5Constraint, unityConstraint);
                        break;
                    case "characterjoint":
                        SetCharacterJointProperties(ue5Constraint, unityConstraint);
                        break;
                    case "configurablejoint":
                        SetConfigurableJointProperties(ue5Constraint, unityConstraint);
                        break;
                }

                ue5Data.PhysicsConstraints.Add(ue5Constraint);
            }


            GenerateCollisionDisableTable(ue5Data, unityData, bodyIndexMap);

            return ue5Data;
        }

        private UE5Transform CreateFrame2TransformFromBodyData(
            UnityPhysicsConstraintData unityConstraint,
            Dictionary<string, UnityPhysicsBodyData> bodyDataMap,
            float metersToCm)
        {
            // 默认值，如果找不到对应的体数据
            var transform = new UE5Transform
            {
                Location = ConvertUnityToUE5Vector(unityConstraint.ConnectedAnchor * metersToCm),
                // Rotation 将在下面根据轴和 bodyData 设置
                Scale = new UE5Vector { X = 1, Y = 1, Z = 1 } // 默认缩放，会被下面的逻辑覆盖（如果找到bodyData）
            };

            // 查找约束对象的第一个组件（Bone1）的数据 -> 注意：这里应该是查找 ConnectedBodyName 对应的 bodyData 来获取其 LocalTransform
            // 但是当前的 Frame2Transform 的 Location 和 Rotation 似乎是基于 Bone1 (unityConstraint.GameObjectName) 的 bodyData
            // 我将保持现有逻辑，即使用 unityConstraint.GameObjectName 查找 bodyData，因为这是对原始代码的最小改动，除非明确指出要更改此查找目标。
            // 如果要基于 ConnectedBodyName, 则应改为 bodyDataMap.TryGetValue(unityConstraint.ConnectedBodyName, out var bodyData)

            if (!string.IsNullOrEmpty(unityConstraint.GameObjectName) &&
                bodyDataMap.TryGetValue(unityConstraint.GameObjectName, out var bodyData))
            {
                // 基于用户示例数据，Frame2Transform.Location应为Unity本地位置*100，并调整坐标系
                transform.Location = new UE5Vector
                {
                    X = -bodyData.LocalPosition.x * metersToCm,
                    Y = bodyData.LocalPosition.z * metersToCm,
                    Z = bodyData.LocalPosition.y * metersToCm
                };

                float d_val = (unityConstraint.HighTwistLimit + unityConstraint.LowTwistLimit) / 2.0f;
                float rollOffset, pitchOffset, yawOffset;

                // 根据 unityConstraint.Axis 设置旋转偏移量
                // (1,0,0) 对应 UnityEngine.Vector3.right
                // (0,1,0) 对应 UnityEngine.Vector3.up
                // (0,0,1) 对应 UnityEngine.Vector3.forward
                if (unityConstraint.Axis == UnityEngine.Vector3.right)
                {
                    rollOffset = -90f;
                    pitchOffset = d_val;
                    yawOffset = 90f;
                }
                else if (unityConstraint.Axis == UnityEngine.Vector3.up)
                {
                    rollOffset = 180f;
                    pitchOffset = 0f;
                    yawOffset = 90f - d_val;
                }
                else if (unityConstraint.Axis == UnityEngine.Vector3.forward)
                {
                    rollOffset = -90f;
                    pitchOffset = d_val;
                    yawOffset = 0f;
                }
                else // 如果 Axis 不是特定值，则使用原始偏移逻辑作为默认值
                {
                    // 原始逻辑的偏移:
                    // Roll offset: -90f
                    // Pitch offset: d_val
                    // Yaw offset: 0f
                    rollOffset = -90f;
                    pitchOffset = d_val;
                    yawOffset = 90f;
                }
                
                // UE Pitch (Y rotation in UE) from Unity Y Euler (Pitch in Unity)
                // UE Yaw (Z rotation in UE) from Unity Z Euler (Yaw in Unity)
                // UE Roll (X rotation in UE) from Unity X Euler (Roll in Unity)
                transform.Rotation = new UE5Rotator
                {
                    Pitch = bodyData.LocalRotationEuler.y + pitchOffset, // Unity Y (Pitch) -> UE Pitch (Y rotation)
                    Yaw = bodyData.LocalRotationEuler.z + yawOffset,     // Unity Z (Yaw)   -> UE Yaw (Z rotation)
                    Roll = bodyData.LocalRotationEuler.x + rollOffset    // Unity X (Roll)  -> UE Roll (X rotation)
                };

                transform.Scale = new UE5Vector
                {
                    X = bodyData.LocalScale.x,
                    Y = bodyData.LocalScale.y,
                    Z = bodyData.LocalScale.z
                };
            }

            return transform;
        }

        private void SetFixedJointProperties(UE5PhysicsConstraint ue5Constraint, UnityPhysicsConstraintData unityConstraint)
        {
            ue5Constraint.LinearLimits = new UE5LinearLimits
            {
                XMotion = "LCM_Locked",
                YMotion = "LCM_Locked",
                ZMotion = "LCM_Locked"
            };

            ue5Constraint.AngularLimits = new UE5AngularLimits
            {
                Swing1Motion = "ACM_Locked",
                Swing2Motion = "ACM_Locked",
                TwistMotion = "ACM_Locked"
            };
        }

        private void GenerateCollisionDisableTable(UE5PhysicsExportData ue5Data, UnityPhysicsExportData unityData, Dictionary<string, int> bodyIndexMap)
        {
            foreach (var constraint in unityData.PhysicsConstraints)
            {
                if (!constraint.EnableCollision)
                {
                    if (bodyIndexMap.TryGetValue(constraint.GameObjectName, out int bodyIndex1) &&
                        bodyIndexMap.TryGetValue(constraint.ConnectedBodyName, out int bodyIndex2))
                    {
                        ue5Data.CollisionDisableTable.Add(new UE5CollisionDisableEntry
                        {
                            BodyIndex1 = bodyIndex1,
                            BodyIndex2 = bodyIndex2
                        });
                    }
                }
            }
        }

        private UE5Vector ConvertUnityToUE5Vector(UnityEngine.Vector3 unityVector)
        {
            return new UE5Vector
            {
                X = -unityVector.x, 
                Y = unityVector.z, 
                Z = unityVector.y 
            };
        }

        private UnityEngine.Vector3 ConvertUE5ToUnityVector(UE5Vector ue5Vector)
        {
            return new UnityEngine.Vector3(
                -ue5Vector.X, 
                ue5Vector.Z, 
                ue5Vector.Y 
            );
        }

        private UE5Quaternion ConvertUnityToUE5Quaternion(UnityEngine.Quaternion unityQuat)
        {
            return new UE5Quaternion
            {
                X = unityQuat.x,
                Y = -unityQuat.z,
                Z = unityQuat.y,
                W = unityQuat.w
            };
        }

        private UnityEngine.Quaternion ConvertUE5ToUnityQuaternion(UE5Quaternion ue5Quat)
        {
            return new UnityEngine.Quaternion(
                ue5Quat.X,
                ue5Quat.Z,
                -ue5Quat.Y,
                ue5Quat.W
            );
        }

        private UE5PhysicalMaterial FindBestMatchingUE5Material(UnityPhysicsBodyData unityBody, Dictionary<string, UE5PhysicalMaterial> cache)
        {
            foreach (var collider in unityBody.Colliders)
            {
                if (!string.IsNullOrEmpty(collider.MaterialName) && cache.TryGetValue(collider.MaterialName, out var mat))
                {
                    return mat;
                }
            }
            return null;
        }

        private UE5Rotator ConvertUnityToUE5Rotator(UnityEngine.Quaternion unityQuat)
        {

            UE5Quaternion ue5Quat = ConvertUnityToUE5Quaternion(unityQuat);





            double SinY_CosP = 2 * (ue5Quat.W * ue5Quat.Y + ue5Quat.Z * ue5Quat.X);
            double CosY_CosP = 1 - 2 * (ue5Quat.Y * ue5Quat.Y + ue5Quat.Z * ue5Quat.Z);
            double yaw = Math.Atan2(SinY_CosP, CosY_CosP);

            double SinP = 2 * (ue5Quat.W * ue5Quat.Z - ue5Quat.X * ue5Quat.Y);
            double pitch;
            if (Math.Abs(SinP) >= 1)

                pitch = (Math.PI / 2) * Math.Sign(SinP);
            else
                pitch = Math.Asin(SinP);

            double SinR_CosP = 2 * (ue5Quat.W * ue5Quat.X + ue5Quat.Y * ue5Quat.Z);
            double CosR_CosP = 1 - 2 * (ue5Quat.Z * ue5Quat.Z + ue5Quat.X * ue5Quat.X);
            double roll = Math.Atan2(SinR_CosP, CosR_CosP);


            const float RadToDeg = (float)(180.0 / Math.PI);

            return new UE5Rotator
            {
                Pitch = (float)pitch * RadToDeg,
                Yaw = (float)yaw * RadToDeg,
                Roll = (float)roll * RadToDeg
            };
        }

        private UnityEngine.Quaternion ConvertUE5RotatorToQuaternion(UE5Rotator ue5Rotator)
        {

            float pitchRad = ue5Rotator.Pitch * Mathf.Deg2Rad;
            float yawRad = ue5Rotator.Yaw * Mathf.Deg2Rad;
            float rollRad = ue5Rotator.Roll * Mathf.Deg2Rad;


            UnityEngine.Quaternion ue5Quat = UnityEngine.Quaternion.AngleAxis(yawRad * Mathf.Rad2Deg, Vector3.forward) *
                                             UnityEngine.Quaternion.AngleAxis(pitchRad * Mathf.Rad2Deg, Vector3.up) *
                                             UnityEngine.Quaternion.AngleAxis(rollRad * Mathf.Rad2Deg, Vector3.right);


            return ConvertUE5ToUnityQuaternion(new UE5Quaternion { X = ue5Quat.x, Y = ue5Quat.y, Z = ue5Quat.z, W = ue5Quat.w });
        }
    }

    public class PhysicsDataConverterWindow : EditorWindow
    {
        private string ue5JsonPath = "";
        private string unityJsonPath = "";
        private string outputPath = "";
        private bool isConverting = false;
        private string statusMessage = "";
        private MessageType statusType = MessageType.Info;

        [MenuItem("Tools/Physics Data Converter")]
        public static void ShowWindow()
        {
            GetWindow<PhysicsDataConverterWindow>("Physics Data Converter");
        }

        private void OnGUI()
        {
            GUILayout.Label("Physics Data Converter", EditorStyles.boldLabel);
            EditorGUILayout.Space();


            EditorGUILayout.LabelField("UE5 to Unity Conversion", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.BeginHorizontal();
            ue5JsonPath = EditorGUILayout.TextField("UE5 JSON Path", ue5JsonPath);
            if (GUILayout.Button("Browse", GUILayout.Width(60)))
            {
                ue5JsonPath = EditorUtility.OpenFilePanel("Select UE5 JSON File", "", "json");
            }
            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Convert UE5 to Unity"))
            {
                ConvertUE5ToUnity();
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space();


            EditorGUILayout.LabelField("Unity to UE5 Conversion", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.BeginHorizontal();
            unityJsonPath = EditorGUILayout.TextField("Unity JSON Path", unityJsonPath);
            if (GUILayout.Button("Browse", GUILayout.Width(60)))
            {
                unityJsonPath = EditorUtility.OpenFilePanel("Select Unity JSON File", "", "json");
            }
            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Convert Unity to UE5"))
            {
                ConvertUnityToUE5();
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space();


            if (!string.IsNullOrEmpty(statusMessage))
            {
                EditorGUILayout.HelpBox(statusMessage, statusType);
            }
        }

        private void ConvertUE5ToUnity()
        {
            if (string.IsNullOrEmpty(ue5JsonPath))
            {
                statusMessage = "Please select a UE5 JSON file first.";
                statusType = MessageType.Error;
                return;
            }

            try
            {
                isConverting = true;
                var converter = new PhysicsDataConverter();
                var unityData = converter.ConvertUE5ToUnity(ue5JsonPath);


                outputPath = Path.Combine(Path.GetDirectoryName(ue5JsonPath),
                                        Path.GetFileNameWithoutExtension(ue5JsonPath) + "_unity.json");
                string unityJson = JsonUtility.ToJson(unityData, true);
                File.WriteAllText(outputPath, unityJson);

                statusMessage = $"Conversion successful! Output saved to: {outputPath}";
                statusType = MessageType.Info;
            }
            catch (System.Exception e)
            {
                statusMessage = $"Conversion failed: {e.Message}";
                statusType = MessageType.Error;
            }
            finally
            {
                isConverting = false;
            }
        }

        private void ConvertUnityToUE5()
        {
            if (string.IsNullOrEmpty(unityJsonPath))
            {
                statusMessage = "Please select a Unity JSON file first.";
                statusType = MessageType.Error;
                return;
            }

            try
            {
                isConverting = true;
                var converter = new PhysicsDataConverter();
                var ue5Data = converter.ConvertUnityToUE5(unityJsonPath);


                outputPath = Path.Combine(Path.GetDirectoryName(unityJsonPath),
                                        Path.GetFileNameWithoutExtension(unityJsonPath) + "_ue5.json");

                var settings = new JsonSerializerSettings
                {
                    Formatting = Formatting.Indented,
                    NullValueHandling = NullValueHandling.Ignore
                };

                string ue5Json = JsonConvert.SerializeObject(ue5Data, settings);
                File.WriteAllText(outputPath, ue5Json);

                statusMessage = $"Conversion successful! Output saved to: {outputPath}";
                statusType = MessageType.Info;
            }
            catch (System.Exception e)
            {
                statusMessage = $"Conversion failed: {e.Message}";
                statusType = MessageType.Error;
            }
            finally
            {
                isConverting = false;
            }
        }
    }
}
